import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { TraceNode } from './types';
import {
    getServiceIcon,
    getStatusIcon,
    getNodeTypeColor,
    formatDuration,
    formatTimestamp
} from './utils';

interface TraceNodeDetailProps {
    node: TraceNode;
}

export const TraceNodeDetail: React.FC<TraceNodeDetailProps> = ({ node }) => {
    const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['basic']));

    const toggleSection = (section: string) => {
        const newExpanded = new Set(expandedSections);
        if (newExpanded.has(section)) {
            newExpanded.delete(section);
        } else {
            newExpanded.add(section);
        }
        setExpandedSections(newExpanded);
    };



    return (
        <Card>
            <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                    <div className={`p-2 rounded border ${getNodeTypeColor(node.type)}`}>
                        {getServiceIcon(node.type)}
                    </div>
                    <div className="flex-1">
                        <div className="flex items-center gap-2">
                            <span>{node.service || node.method || node.id}</span>
                            {getStatusIcon(node.status)}
                        </div>
                        <div className="text-sm text-muted-foreground font-normal">
                            {node.type.toUpperCase()} • {formatDuration(node.duration)}
                        </div>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                {/* 基本信息 */}
                <Collapsible 
                    open={expandedSections.has('basic')} 
                    onOpenChange={() => toggleSection('basic')}
                >
                    <CollapsibleTrigger asChild>
                        <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                            <span className="font-medium">基本信息</span>
                            <ChevronDown className={`h-4 w-4 transition-transform ${
                                expandedSections.has('basic') ? 'rotate-180' : ''
                            }`} />
                        </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span className="text-muted-foreground">节点ID:</span>
                                <div className="font-mono">{node.id}</div>
                            </div>
                            <div>
                                <span className="text-muted-foreground">服务类型:</span>
                                <div className="flex items-center gap-1">
                                    {getServiceIcon(node.type)}
                                    <span className="capitalize">{node.type}</span>
                                </div>
                            </div>
                            <div>
                                <span className="text-muted-foreground">执行时间:</span>
                                <div>{formatDuration(node.duration)}</div>
                            </div>
                            <div>
                                <span className="text-muted-foreground">状态:</span>
                                <div className="flex items-center gap-1">
                                    {getStatusIcon(node.status)}
                                    <span className="capitalize">{node.status}</span>
                                </div>
                            </div>
                            <div className="col-span-2">
                                <span className="text-muted-foreground">时间戳:</span>
                                <div className="font-mono">{formatTimestamp(node.timestamp)}</div>
                            </div>
                        </div>
                    </CollapsibleContent>
                </Collapsible>

                {/* 标签信息 */}
                {node.tags && node.tags.length > 0 && (
                    <Collapsible 
                        open={expandedSections.has('tags')} 
                        onOpenChange={() => toggleSection('tags')}
                    >
                        <CollapsibleTrigger asChild>
                            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                                <span className="font-medium">标签</span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${
                                    expandedSections.has('tags') ? 'rotate-180' : ''
                                }`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="mt-2">
                            <div className="flex flex-wrap gap-2">
                                {node.tags.map((tag: string, index: number) => (
                                    <Badge key={index} variant="secondary" className="text-xs">
                                        {tag}
                                    </Badge>
                                ))}
                            </div>
                        </CollapsibleContent>
                    </Collapsible>
                )}

                {/* 请求信息 */}
                {node.request && (
                    <Collapsible 
                        open={expandedSections.has('request')} 
                        onOpenChange={() => toggleSection('request')}
                    >
                        <CollapsibleTrigger asChild>
                            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                                <span className="font-medium">请求信息</span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${
                                    expandedSections.has('request') ? 'rotate-180' : ''
                                }`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="mt-2">
                            <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
                                {JSON.stringify(node.request, null, 2)}
                            </pre>
                        </CollapsibleContent>
                    </Collapsible>
                )}

                {/* 响应信息 */}
                {node.response && (
                    <Collapsible 
                        open={expandedSections.has('response')} 
                        onOpenChange={() => toggleSection('response')}
                    >
                        <CollapsibleTrigger asChild>
                            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                                <span className="font-medium">响应信息</span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${
                                    expandedSections.has('response') ? 'rotate-180' : ''
                                }`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="mt-2">
                            <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
                                {JSON.stringify(node.response, null, 2)}
                            </pre>
                        </CollapsibleContent>
                    </Collapsible>
                )}

                {/* SQL 信息 */}
                {node.sql && (
                    <Collapsible 
                        open={expandedSections.has('sql')} 
                        onOpenChange={() => toggleSection('sql')}
                    >
                        <CollapsibleTrigger asChild>
                            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                                <span className="font-medium">SQL 语句</span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${
                                    expandedSections.has('sql') ? 'rotate-180' : ''
                                }`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="mt-2">
                            <pre className="bg-muted p-3 rounded text-xs overflow-x-auto font-mono">
                                {node.sql}
                            </pre>
                        </CollapsibleContent>
                    </Collapsible>
                )}
            </CardContent>
        </Card>
    );
};
