import React, { ReactElement } from 'react';
import { 
    Globe,
    Database,
    Server,
    MessageSquare,
    Bot,
    Search,
    Layers,
    Zap,
    Clock,
    CheckCircle,
    XCircle,
    AlertCircle
} from 'lucide-react';
import { ServiceType, NodeStatus } from './types';

// 服务图标配置
export const getServiceIcon = (type: ServiceType): ReactElement => {
    switch (type) {
        case 'http': return <Globe className="h-3 w-3"/>;
        case 'thrift': return <Zap className="h-3 w-3"/>;
        case 'db': return <Database className="h-3 w-3"/>;
        case 'redis': return <Server className="h-3 w-3"/>;
        case 'mq': return <MessageSquare className="h-3 w-3"/>;
        case 'agent': return <Bot className="h-3 w-3"/>;
        case 'search': return <Search className="h-3 w-3"/>;
        case 'vector': return <Layers className="h-3 w-3"/>;
        default: return <Server className="h-3 w-3"/>;
    }
};

// 状态图标配置
export const getStatusIcon = (status: NodeStatus): ReactElement => {
    switch (status) {
        case 'success': return <CheckCircle className="h-3 w-3 text-green-500"/>;
        case 'error': return <XCircle className="h-3 w-3 text-red-500"/>;
        case 'timeout': return <AlertCircle className="h-3 w-3 text-yellow-500"/>;
        case 'pending': return <Clock className="h-3 w-3 text-blue-500"/>;
        default: return <CheckCircle className="h-3 w-3 text-gray-500"/>;
    }
};

// 节点类型颜色配置
export const getNodeTypeColor = (type: ServiceType): string => {
    switch (type) {
        case 'http': return 'bg-blue-50 border-blue-200 text-blue-800';
        case 'thrift': return 'bg-orange-50 border-orange-200 text-orange-800';
        case 'db': return 'bg-green-50 border-green-200 text-green-800';
        case 'redis': return 'bg-red-50 border-red-200 text-red-800';
        case 'mq': return 'bg-purple-50 border-purple-200 text-purple-800';
        case 'agent': return 'bg-indigo-50 border-indigo-200 text-indigo-800';
        case 'search': return 'bg-teal-50 border-teal-200 text-teal-800';
        case 'vector': return 'bg-pink-50 border-pink-200 text-pink-800';
        default: return 'bg-gray-50 border-gray-200 text-gray-800';
    }
};

// 格式化持续时间
export const formatDuration = (duration: number): string => {
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(2)}s`;
};

// 格式化时间戳
export const formatTimestamp = (timestamp: Date): string => {
    return timestamp.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};
