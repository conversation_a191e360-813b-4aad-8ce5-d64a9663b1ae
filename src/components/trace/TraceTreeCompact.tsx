import React, {ReactElement, useState} from 'react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {ChevronDown, ChevronRight, Eye, EyeOff, Hash} from 'lucide-react';
import {TraceNode} from './types';
import {formatDuration, getNodeTypeColor, getServiceIcon, getStatusIcon} from './utils';
import {TraceNodeDetail} from './TraceNodeDetail';

interface TraceTreeCompactProps {
    traceData?: TraceNode;
}

export const TraceTreeCompact: React.FC<TraceTreeCompactProps> = ({traceData}) => {
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));
    const [selectedNode, setSelectedNode] = useState<TraceNode | null>(null);
    const [highlightedPath, setHighlightedPath] = useState<Set<string>>(new Set());

    // 切换节点展开状态
    const toggleExpanded = (nodeId: string) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(nodeId)) {
            newExpanded.delete(nodeId);
        } else {
            newExpanded.add(nodeId);
        }
        setExpandedNodes(newExpanded);
    };

    // 计算从根节点到目标节点的路径
    const findPathToNode = (rootNode: TraceNode, targetNodeId: string, currentPath: string[] = []): string[] | null => {
        const newPath = [...currentPath, rootNode.id];

        if (rootNode.id === targetNodeId) {
            return newPath;
        }

        if (rootNode.children) {
            for (const child of rootNode.children) {
                const result = findPathToNode(child, targetNodeId, newPath);
                if (result) {
                    return result;
                }
            }
        }

        return null;
    };

    // 处理节点选择
    const handleNodeSelect = (node: TraceNode, rootNode: TraceNode) => {
        setSelectedNode(node);
        const path = findPathToNode(rootNode, node.id);
        if (path) {
            console.log('高亮路径:', path); // 调试信息
            setHighlightedPath(new Set(path));
        } else {
            setHighlightedPath(new Set());
        }
    };


    const renderIndentLines = (level: number, isLast: boolean, ancestorLines: boolean[] = [], nodeId: string): ReactElement[] => {
        if (level === 0) return [];

        const indentElements: ReactElement[] = [];
        const INDENT_WIDTH = 20;
        const LINE_OFFSET = 9.5;

        for (let i = 0; i < level; i++) {
            const isCurrentLevel = i === level - 1;

            // 检查当前连线是否应该高亮
            // 连线高亮的条件：当前节点在高亮路径中
            const shouldHighlight = highlightedPath.has(nodeId);

            if (isCurrentLevel) {
                indentElements.push(
                    <span
                        key={`current-${i}`}
                        className="inline-block relative"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px',
                            contain: 'layout'
                        }}
                    >
                        {!isLast && (
                            <span
                                className={`absolute ${shouldHighlight ? 'w-1 bg-blue-600 shadow-sm' : 'w-px bg-emerald-500'}`}
                                style={{
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    transform: 'translateZ(0)',
                                    willChange: 'transform',
                                    imageRendering: 'pixelated',
                                    backfaceVisibility: 'hidden'
                                }}
                            ></span>
                        )}

                        <span
                            className={`absolute border-l border-b rounded-bl-lg ${
                                shouldHighlight
                                    ? 'border-blue-600 border-2 shadow-sm'
                                    : 'border-emerald-500'
                            }`}
                            style={{
                                left: `${LINE_OFFSET}px`,
                                top: '0',
                                width: '10px',
                                height: '50%',
                                transform: 'translateZ(0)',
                                willChange: 'transform',
                                imageRendering: 'pixelated',
                                backfaceVisibility: 'hidden'
                            }}
                        ></span>
                    </span>
                );
            } else {
                const shouldShowLine = ancestorLines[i];
                indentElements.push(
                    <span
                        key={`ancestor-${i}`}
                        className="inline-block relative"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px',
                            contain: 'layout'
                        }}
                    >
                        {shouldShowLine && (
                            <span
                                className={`absolute ${shouldHighlight ? 'w-0.5 bg-blue-500' : 'w-px bg-emerald-500'}`}
                                style={{
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    transform: 'translateZ(0)',
                                    willChange: 'transform',
                                    imageRendering: 'pixelated',
                                    backfaceVisibility: 'hidden'
                                }}
                            ></span>
                        )}
                    </span>
                );
            }
        }

        return indentElements;
    };

    const renderNode = (node: TraceNode, level: number, isLast: boolean, ancestorLines: boolean[], parentPath: string[] = [], rootNode: TraceNode): ReactElement => {
        const isExpanded = expandedNodes.has(node.id);
        const hasChildren = node.children && node.children.length > 0;

        const currentAncestorLines = [...ancestorLines];
        if (level > 0) {
            currentAncestorLines[level - 1] = !isLast;
        }

        // 构建当前节点的路径
        const currentPath = [...parentPath, node.id];

        return (
            <div key={node.id} className="relative">
                <div
                    className={`flex items-center min-h-[28px] hover:bg-muted/50 cursor-pointer px-1 ${
                        selectedNode?.id === node.id ? 'bg-muted' : ''
                    }`}
                    onClick={() => handleNodeSelect(node, rootNode)}
                >
                    {level > 0 && renderIndentLines(level, isLast, ancestorLines, node.id)}

                    <div className="flex items-center gap-2 flex-1 min-w-0">
                        <div className={`p-1 rounded border flex-shrink-0 ${getNodeTypeColor(node.type)}`}>
                            {getServiceIcon(node.type)}
                        </div>

                        <span className="text-sm font-medium truncate flex-1">
                            {node.service || node.method || node.id}
                        </span>

                        <span className="text-xs text-muted-foreground flex-shrink-0">
                            {formatDuration(node.duration)}
                        </span>

                        <div className="flex-shrink-0">
                            {getStatusIcon(node.status)}
                        </div>

                        {hasChildren && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-4 w-4 p-0 flex-shrink-0"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    toggleExpanded(node.id);
                                }}
                            >
                                {isExpanded ? <ChevronDown className="h-3 w-3"/> : <ChevronRight className="h-3 w-3"/>}
                            </Button>
                        )}
                    </div>
                </div>

                {hasChildren && isExpanded && (
                    <div className="relative">
                        {node.children!.map((child, index) =>
                            renderNode(
                                child,
                                level + 1,
                                index === node.children!.length - 1,
                                currentAncestorLines,
                                currentPath,
                                rootNode
                            )
                        )}
                    </div>
                )}
            </div>
        );
    };

    const sampleData: TraceNode = traceData || {
        id: 'root',
        type: 'agent',
        service: "O'Keefe Inc-service",
        method: 'processRequest',
        duration: 1560,
        status: 'success',
        timestamp: new Date(),
        tags: ['production', 'high-priority', 'user-request'],
        request: {
            method: 'POST',
            url: '/api/v1/process',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ***'
            },
            body: {
                userId: '12345',
                action: 'process_data'
            }
        },
        response: {
            status: 200,
            data: {
                result: 'success',
                processedItems: 42
            }
        },
        children: [
            {
                id: 'redis-1',
                type: 'redis',
                service: 'redis-cluster',
                method: 'GET',
                duration: 1930,
                status: 'error',
                timestamp: new Date(),
                tags: ['cache', 'read-operation'],
                request: {
                    command: 'GET',
                    key: 'user:12345:profile'
                },
                response: {
                    error: 'Connection timeout',
                    code: 'TIMEOUT'
                },
                children: [
                    {
                        id: 'mysql-1',
                        type: 'db',
                        service: 'mysql-cluster-67',
                        method: 'SELECT',
                        duration: 1440,
                        status: 'timeout',
                        timestamp: new Date(),
                        tags: ['database', 'user-data'],
                        sql: 'SELECT id, name, email, profile_data FROM users WHERE id = ? AND status = ?',
                        request: {
                            query: 'SELECT users',
                            params: ['12345', 'active']
                        },
                        children: [
                            {
                                id: 'redis-2',
                                type: 'redis',
                                service: 'redis-cluster',
                                method: 'SET',
                                duration: 1290,
                                status: 'success',
                                timestamp: new Date(),
                                tags: ['cache', 'write-operation'],
                                request: {
                                    command: 'SET',
                                    key: 'user:12345:profile',
                                    value: '{"id":12345,"name":"John Doe"}'
                                },
                                response: {
                                    result: 'OK'
                                },
                                children: [
                                    {
                                        id: 'thrift-1',
                                        type: 'thrift',
                                        service: 'Schoppe and Sons-service',
                                        method: 'getUserPreferences',
                                        duration: 1440,
                                        status: 'success',
                                        timestamp: new Date(),
                                        tags: ['rpc', 'user-preferences'],
                                        request: {
                                            method: 'getUserPreferences',
                                            args: {
                                                userId: '12345',
                                                includeDefaults: true
                                            }
                                        },
                                        response: {
                                            preferences: {
                                                theme: 'dark',
                                                language: 'zh-CN',
                                                notifications: true
                                            }
                                        },
                                        children: []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    };

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Hash className="h-4 w-4"/>
                            链路追踪详情
                        </div>
                        <div className="flex gap-1">
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs px-2"
                                onClick={() => setExpandedNodes(new Set())}
                            >
                                <EyeOff className="h-3 w-3 mr-1"/>
                                折叠
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs px-2"
                                onClick={() => {
                                    const allIds = new Set<string>();
                                    const collectIds = (node: TraceNode) => {
                                        allIds.add(node.id);
                                        if (node.children) {
                                            node.children.forEach(collectIds);
                                        }
                                    };
                                    collectIds(sampleData);
                                    setExpandedNodes(allIds);
                                }}
                            >
                                <Eye className="h-3 w-3 mr-1"/>
                                展开
                            </Button>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent className="p-3">
                    <div
                        className="transform-gpu"
                        style={{
                            transform: 'translateZ(0)',
                            willChange: 'transform',
                            contain: 'layout style'
                        }}
                    >
                        {renderNode(sampleData, 0, true, [], [], sampleData)}
                    </div>
                </CardContent>
            </Card>

            {/* 选中节点详情 */}
            {selectedNode && <TraceNodeDetail node={selectedNode}/>}
        </div>
    );
};


