// 服务类型
export type ServiceType = 'http' | 'thrift' | 'db' | 'redis' | 'mq' | 'agent' | 'search' | 'vector';

// 节点状态
export type NodeStatus = 'success' | 'error' | 'timeout' | 'pending';

// 追踪节点接口
export interface TraceNode {
    id: string;
    type: ServiceType;
    service?: string;
    method?: string;
    duration: number;
    status: NodeStatus;
    timestamp: Date;
    tags?: string[];
    children?: TraceNode[];
    request?: Record<string, unknown>;
    response?: Record<string, unknown>;
    sql?: string;
}
