<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TraceTree 高亮功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .test-steps {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
        }
        .step {
            margin-bottom: 8px;
        }
        .expected {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .blue-highlight {
            background: #2196f3;
            color: white;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TraceTree 高亮功能测试指南</h1>
        
        <div class="test-section">
            <div class="test-title">功能说明</div>
            <div class="test-description">
                当用户点击 TraceTree 中的某个节点时，从根节点到该节点的路径上的连线应该高亮变粗。
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试步骤</div>
            <div class="test-steps">
                <div class="step">1. 打开浏览器访问: <code>http://localhost:8081/trace-test</code></div>
                <div class="step">2. 确保所有节点都展开（点击右上角的"展开"按钮）</div>
                <div class="step">3. 点击树中的任意一个节点</div>
                <div class="step">4. 观察从根节点到被点击节点的连线是否高亮</div>
                <div class="step">5. 点击不同深度的节点进行测试</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">预期效果</div>
            <div class="expected">
                <strong>正常连线：</strong> 细线，绿色 (emerald-500)<br>
                <strong>高亮连线：</strong> <span class="blue-highlight">粗线，蓝色 (blue-600)，带阴影</span><br><br>
                
                <strong>测试场景：</strong><br>
                • 点击根节点：无连线高亮<br>
                • 点击第一层节点：根节点到该节点的连线高亮<br>
                • 点击深层节点：整条路径上的所有连线都应该高亮<br>
                • 点击不同节点：高亮应该切换到新的路径
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">调试信息</div>
            <div class="test-description">
                打开浏览器开发者工具的控制台，点击节点时应该能看到类似以下的调试信息：
                <pre>高亮路径: ['root', 'redis-1', 'mysql-1', 'redis-2']</pre>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">故障排除</div>
            <div class="test-steps">
                <div class="step"><strong>如果连线没有高亮：</strong></div>
                <div class="step">• 检查控制台是否有错误信息</div>
                <div class="step">• 确认调试信息中的路径是否正确</div>
                <div class="step">• 检查 CSS 类是否正确应用</div>
                <div class="step"><strong>如果高亮效果不明显：</strong></div>
                <div class="step">• 可以调整蓝色的深度或线条粗细</div>
                <div class="step">• 可以添加更多视觉效果如动画或发光</div>
            </div>
        </div>
    </div>
</body>
</html>
